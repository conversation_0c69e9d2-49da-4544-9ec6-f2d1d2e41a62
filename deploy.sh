#!/bin/bash

echo "🚀 Production Deployment Script"
echo "==============================="

# Renk kodları
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Hata kontrolü
set -e

echo -e "${BLUE}📋 Production deployment başlatılıyor...${NC}"

# Backend production setup
echo -e "${YELLOW}🔧 Backend production konfigürasyonu...${NC}"
cd backend

# Environment'ı production'a çek
sed -i 's/APP_ENV=local/APP_ENV=production/' .env
sed -i 's/APP_DEBUG=true/APP_DEBUG=false/' .env

# Cache'leri temizle ve optimize et
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

# Production cache'leri oluştur
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Composer optimize
composer install --optimize-autoloader --no-dev

echo -e "${GREEN}✅ Backend production hazır!${NC}"

# Frontend production build
echo -e "${YELLOW}🔧 Frontend production build...${NC}"
cd ../frontend

# Production build
npm run build

echo -e "${GREEN}✅ Frontend production build tamamlandı!${NC}"

cd ..

echo -e "${GREEN}🎉 Production deployment tamamlandı!${NC}"
echo ""
echo -e "${BLUE}📋 Production sunucu komutları:${NC}"
echo ""
echo -e "${YELLOW}Backend:${NC}"
echo "cd backend && php artisan serve --host=0.0.0.0 --port=8000 --env=production"
echo ""
echo -e "${YELLOW}Frontend:${NC}"
echo "cd frontend && npm start"
echo ""
echo -e "${RED}⚠️  Önemli:${NC}"
echo "- .env dosyasındaki API anahtarlarını kontrol edin"
echo "- HTTPS kullanmayı unutmayın"
echo "- Firewall ayarlarını kontrol edin"
