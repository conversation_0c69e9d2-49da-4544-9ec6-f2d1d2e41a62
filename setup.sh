#!/bin/bash

echo "🚀 Sesli AI Asistan Kurulum Scripti"
echo "=================================="

# Renk kodları
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Hata kontrolü
set -e

echo -e "${BLUE}📋 Sistem gereksinimlerini kontrol ediliyor...${NC}"

# PHP kontrolü
if ! command -v php &> /dev/null; then
    echo -e "${RED}❌ PHP bulunamadı. Lütfen PHP 8.1+ kurun.${NC}"
    exit 1
fi

# Composer kontrolü
if ! command -v composer &> /dev/null; then
    echo -e "${RED}❌ Composer bulunamadı. Lütfen Composer kurun.${NC}"
    exit 1
fi

# Node.js kontrolü
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js bulunamadı. Lütfen Node.js 18+ kurun.${NC}"
    exit 1
fi

# npm kontrolü
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm bulunamadı. Lütfen npm kurun.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Tüm gereksinimler karşılanıyor!${NC}"

echo -e "${BLUE}🔧 Backend kurulumu başlatılıyor...${NC}"

# Backend dizinine git
cd backend

# Composer bağımlılıklarını kur
echo -e "${YELLOW}📦 Composer bağımlılıkları kuruluyor...${NC}"
composer install

# .env dosyasını kopyala
if [ ! -f .env ]; then
    echo -e "${YELLOW}📝 .env dosyası oluşturuluyor...${NC}"
    cp .env.example .env
fi

# Uygulama anahtarını oluştur
echo -e "${YELLOW}🔑 Uygulama anahtarı oluşturuluyor...${NC}"
php artisan key:generate

# Veritabanını oluştur
echo -e "${YELLOW}🗄️ Veritabanı oluşturuluyor...${NC}"
php artisan migrate --force

# Storage linkini oluştur
echo -e "${YELLOW}🔗 Storage linki oluşturuluyor...${NC}"
php artisan storage:link

# Storage dizinlerini oluştur
echo -e "${YELLOW}📁 Storage dizinleri oluşturuluyor...${NC}"
mkdir -p storage/app/public/audio/uploads
mkdir -p storage/app/public/audio/responses

echo -e "${GREEN}✅ Backend kurulumu tamamlandı!${NC}"

# Frontend dizinine git
cd ../frontend

echo -e "${BLUE}🔧 Frontend kurulumu başlatılıyor...${NC}"

# npm bağımlılıklarını kur
echo -e "${YELLOW}📦 npm bağımlılıkları kuruluyor...${NC}"
npm install

# .env.local dosyasını oluştur
if [ ! -f .env.local ]; then
    echo -e "${YELLOW}📝 .env.local dosyası oluşturuluyor...${NC}"
    echo "NEXT_PUBLIC_API_URL=http://localhost:8000/api" > .env.local
fi

echo -e "${GREEN}✅ Frontend kurulumu tamamlandı!${NC}"

# Ana dizine dön
cd ..

echo -e "${GREEN}🎉 Kurulum başarıyla tamamlandı!${NC}"
echo ""
echo -e "${BLUE}📋 Sonraki adımlar:${NC}"
echo ""
echo -e "${YELLOW}1. API Anahtarlarını Yapılandırın:${NC}"
echo "   backend/.env dosyasını düzenleyin ve aşağıdaki anahtarları ekleyin:"
echo "   - OPENAI_API_KEY (https://platform.openai.com/api-keys)"
echo "   - ELEVENLABS_API_KEY (https://elevenlabs.io/app/settings/api-keys)"
echo ""
echo -e "${YELLOW}2. Sunucuları Başlatın:${NC}"
echo ""
echo -e "${BLUE}   Terminal 1 - Backend:${NC}"
echo "   cd backend"
echo "   php artisan serve --host=0.0.0.0 --port=8000"
echo ""
echo -e "${BLUE}   Terminal 2 - Frontend:${NC}"
echo "   cd frontend"
echo "   npm run dev"
echo ""
echo -e "${YELLOW}3. Uygulamayı Açın:${NC}"
echo "   http://localhost:3000"
echo ""
echo -e "${GREEN}💡 İpucu: API anahtarları olmadan demo modu ile test edebilirsiniz!${NC}"
echo ""
echo -e "${BLUE}📚 Daha fazla bilgi için README.md dosyasını okuyun.${NC}"
