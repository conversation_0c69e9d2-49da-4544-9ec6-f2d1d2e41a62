# Sesli AI Asistan - Voice Chatbot

B<PERSON> proje, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ın Space tuşuna basarak sesli olarak AI asistanı ile etkileşim kurabildiği modern bir web uygulamasıdır.

## 🚀 Özellikler

- **<PERSON><PERSON><PERSON>**: Space tuşuna basılı tutarak konuşun
- **Speech-to-Text**: OpenAI Whisper ile ses metne dönüştürme
- **AI Yanıtları**: ChatGPT Assistant API ile akıllı yanıtlar
- **Text-to-Speech**: ElevenLabs ile metni sese dönüştürme
- **Real-time UI**: Modern React/Next.js arayüzü
- **Responsive Design**: Mobil ve masaüstü uyumlu

## 🏗️ Teknoloji Stack

### Backend (Laravel 11)
- **Framework**: Laravel 11
- **API**: RESTful API endpoints
- **Services**: OpenAI Whisper, ChatGPT Assistant, ElevenLabs
- **Storage**: Se<PERSON> dosyaları için local storage
- **CORS**: Frontend ile güvenli iletişim

### Frontend (Next.js 14)
- **Framework**: Next.js 14 with TypeScript
- **Styling**: Tailwind CSS
- **Audio**: Web Audio API
- **HTTP Client**: Axios
- **UI**: Modern, responsive design

## 📋 Gereksinimler

- PHP 8.1+
- Composer
- Node.js 18+
- npm/yarn
- OpenAI API Key
- ElevenLabs API Key

## 🛠️ Kurulum

### 1. Repository'yi klonlayın
```bash
git clone <repository-url>
cd chatbot
```

### 2. Backend Kurulumu (Laravel)
```bash
cd backend

# Bağımlılıkları yükleyin
composer install

# Environment dosyasını konfigüre edin
cp .env.example .env

# Uygulama anahtarını oluşturun
php artisan key:generate

# Veritabanını oluşturun
php artisan migrate

# Serveri başlatın
php artisan serve --host=0.0.0.0 --port=8000
```

### 3. Frontend Kurulumu (Next.js)
```bash
cd frontend

# Bağımlılıkları yükleyin
npm install

# Environment dosyasını oluşturun
echo "NEXT_PUBLIC_API_URL=http://localhost:8000/api" > .env.local

# Development serveri başlatın
npm run dev
```

## ⚙️ Konfigürasyon

### Backend (.env)
```env
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ASSISTANT_ID=your_assistant_id_here

# ElevenLabs API Configuration
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
ELEVENLABS_VOICE_ID=pNInz6obpgDQGcFmaJgB

# CORS Configuration
FRONTEND_URL=http://localhost:3000
```

### Frontend (.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:8000/api
```

## 🎯 Kullanım

1. **Tarayıcıda açın**: http://localhost:3000
2. **Mikrofon izni verin**: Tarayıcı mikrofon erişimi isteyecek
3. **Konuşmaya başlayın**: 
   - Space tuşuna basılı tutun
   - Konuşun
   - Space tuşunu bırakın
4. **Yanıtı dinleyin**: AI asistanının sesli yanıtını dinleyin

## 🔧 API Endpoints

### Audio Processing
- `POST /api/audio/process` - Ses dosyasını işle
- `POST /api/audio/stream` - Real-time ses streaming

### Chat
- `POST /api/chat/message` - Metin mesajı gönder

### Health Check
- `GET /api/health` - Sistem durumu kontrolü

## 🎨 UI Bileşenleri

### VoiceChatbot
Ana ses chatbot bileşeni:
- Ses kaydı kontrolü
- Space tuşu event handling
- Mesaj geçmişi
- Loading animasyonları
- Audio player

## 🔒 Güvenlik

- CORS konfigürasyonu
- API rate limiting (opsiyonel)
- Ses dosyası boyut sınırlaması (10MB)
- Input validation

## 🚀 Production Deployment

### Backend
```bash
# Optimize autoloader
composer install --optimize-autoloader --no-dev

# Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Set production environment
APP_ENV=production
APP_DEBUG=false
```

### Frontend
```bash
# Build for production
npm run build

# Start production server
npm start
```

## 🐛 Troubleshooting

### Mikrofon Erişimi
- Tarayıcı ayarlarından mikrofon iznini kontrol edin
- HTTPS kullanın (production'da gerekli)

### API Bağlantı Sorunları
- Backend serverinin çalıştığından emin olun
- CORS ayarlarını kontrol edin
- API anahtarlarının doğru olduğunu kontrol edin

### Ses Kalitesi
- Mikrofon kalitesini kontrol edin
- Arka plan gürültüsünü minimize edin
- İnternet bağlantısının stabil olduğundan emin olun

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📞 İletişim

Sorularınız için issue açabilir veya iletişime geçebilirsiniz.
