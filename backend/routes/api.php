<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AudioController;
use App\Http\Controllers\Api\ChatController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Audio processing routes
Route::prefix('audio')->middleware('throttle:10,1')->group(function () {
    Route::post('/process', [AudioController::class, 'processAudio']);
    Route::post('/stream', [AudioController::class, 'streamAudio']);
});

// Chat routes
Route::prefix('chat')->middleware('throttle:20,1')->group(function () {
    Route::post('/message', [ChatController::class, 'sendMessage']);
});

// Health check
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now(),
        'services' => [
            'openai' => !empty(env('OPENAI_API_KEY')),
            'elevenlabs' => !empty(env('ELEVENLABS_API_KEY')),
        ]
    ]);
});
