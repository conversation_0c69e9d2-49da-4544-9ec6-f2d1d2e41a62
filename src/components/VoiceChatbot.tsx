'use client';

import { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { getErrorMessage, getMicrophoneErrorMessage } from '@/utils/errorMessages';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  text: string;
  audioUrl?: string;
  timestamp: Date;
}

const VoiceChatbot = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isSpacePressed, setIsSpacePressed] = useState(false);
  const [isDemoMode, setIsDemoMode] = useState(false);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);

  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

  // Space tuşu event listener'ları
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.code === 'Space' && !isSpacePressed && !isProcessing) {
        event.preventDefault();
        setIsSpacePressed(true);
        startRecording();
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      if (event.code === 'Space' && isSpacePressed) {
        event.preventDefault();
        setIsSpacePressed(false);
        stopRecording();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [isSpacePressed, isProcessing]);

  // Mikrofon erişimi ve kayıt başlatma
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        } 
      });
      
      streamRef.current = stream;
      audioChunksRef.current = [];

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        processAudio();
      };

      mediaRecorder.start();
      setIsRecording(true);
      
    } catch (error: any) {
      console.error('Mikrofon erişimi hatası:', error);
      alert(getMicrophoneErrorMessage(error));
    }
  };

  // Kayıt durdurma
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      // Stream'i temizle
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }
    }
  };

  // Demo modu için simüle edilmiş yanıt
  const processDemoAudio = async () => {
    setIsProcessing(true);

    // Kullanıcı mesajını ekle
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      text: 'Demo ses kaydı (gerçek ses tanıma için API anahtarları gerekli)',
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);

    // 2 saniye bekle (gerçek API çağrısını simüle et)
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Demo yanıtı ekle
    const assistantMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      type: 'assistant',
      text: 'Bu bir demo yanıtıdır. Gerçek AI yanıtları için OpenAI ve ElevenLabs API anahtarlarını backend/.env dosyasına ekleyin.',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, assistantMessage]);
    setIsProcessing(false);
  };

  // Ses dosyasını işleme
  const processAudio = async () => {
    if (audioChunksRef.current.length === 0) return;

    // Demo modunda simüle edilmiş işlem
    if (isDemoMode) {
      return processDemoAudio();
    }

    setIsProcessing(true);

    try {
      // Ses blob'unu oluştur
      const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });

      // FormData oluştur
      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.webm');

      // Kullanıcı mesajını ekle (geçici)
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'user',
        text: 'Ses kaydı işleniyor...',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, userMessage]);

      // Backend'e gönder
      const response = await axios.post(`${API_BASE_URL}/audio/process`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 30000, // 30 saniye timeout
      });

      if (response.data.success) {
        // Kullanıcı mesajını güncelle
        const updatedUserMessage: ChatMessage = {
          ...userMessage,
          text: response.data.transcription,
        };

        // Asistan yanıtını ekle
        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'assistant',
          text: response.data.response_text,
          audioUrl: response.data.audio_url,
          timestamp: new Date(),
        };

        setMessages(prev => [
          ...prev.slice(0, -1), // Son mesajı çıkar
          updatedUserMessage,
          assistantMessage
        ]);

        // Yanıt sesini çal
        if (response.data.audio_url) {
          const audio = new Audio(response.data.audio_url);
          audio.play().catch(console.error);
        }

      } else {
        throw new Error(response.data.error || 'Ses işleme hatası');
      }

    } catch (error: any) {
      console.error('Ses işleme hatası:', error);

      const errorText = getErrorMessage(error);

      // Hata mesajı ekle
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'assistant',
        text: errorText,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev.slice(0, -1), errorMessage]);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Chat Mesajları */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6 h-96 overflow-y-auto">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 dark:text-gray-400 mt-20">
            <p className="text-lg mb-2">Konuşmaya başlamak için Space tuşuna basılı tutun</p>
            <p className="text-sm">🎤 Mikrofon hazır</p>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.type === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white'
                  }`}
                >
                  <p className="text-sm">{message.text}</p>
                  {message.audioUrl && (
                    <audio controls className="mt-2 w-full">
                      <source src={message.audioUrl} type="audio/mpeg" />
                    </audio>
                  )}
                  <p className="text-xs opacity-70 mt-1">
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Kontrol Paneli */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        {/* Demo Mode Toggle */}
        <div className="flex justify-center mb-4">
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={isDemoMode}
              onChange={(e) => setIsDemoMode(e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Demo Modu (API anahtarları olmadan test)
            </span>
          </label>
        </div>

        <div className="text-center">
          {/* Kayıt Butonu */}
          <div className="mb-4">
            <button
              className={`w-20 h-20 rounded-full flex items-center justify-center text-white text-2xl transition-all duration-200 ${
                isRecording
                  ? 'bg-red-500 animate-pulse scale-110'
                  : isProcessing
                  ? 'bg-yellow-500 animate-spin'
                  : 'bg-blue-500 hover:bg-blue-600'
              }`}
              disabled={isProcessing}
            >
              {isProcessing ? '⏳' : isRecording ? '🔴' : '🎤'}
            </button>
          </div>

          {/* Durum Metni */}
          <div className="text-center">
            {isRecording && (
              <p className="text-red-500 font-medium animate-pulse">
                🔴 Kayıt yapılıyor... Space tuşunu bırakın
              </p>
            )}
            {isProcessing && (
              <p className="text-yellow-500 font-medium">
                ⏳ Ses işleniyor...
              </p>
            )}
            {!isRecording && !isProcessing && (
              <p className="text-gray-600 dark:text-gray-400">
                Space tuşuna basılı tutarak konuşun
              </p>
            )}
          </div>

          {/* Yardım Metni */}
          <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
            <p>💡 İpucu: Space tuşuna basılı tutun, konuşun ve bırakın</p>
            {isDemoMode && (
              <p className="mt-2 text-yellow-600 dark:text-yellow-400">
                🧪 Demo modu aktif - Gerçek API çağrıları yapılmayacak
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VoiceChatbot;
